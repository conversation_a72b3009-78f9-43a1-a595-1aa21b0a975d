package com.pass.hbl.manager.backend.persistence.util;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

/**
 * Utility component for batch deletion operations with season-specific filtering
 */
@Component
@Slf4j
public class BatchDeletionUtil {

    @Autowired
    private JdbcTemplate jdbcTemplate;

    /**
     * Delete records by season ID with configurable batch size and wait time
     */
    @Transactional
    public long deleteBySeasonId(String tableName, String seasonId, int batchSize, long waitTimeMs) {
        if (StringUtils.isBlank(seasonId)) {
            log.info("No season ID provided for table {}, skipping deletion", tableName);
            return 0L;
        }

        log.info("Deleting from {} for season {} in batches of {}", tableName, seasonId, batchSize);

        String deleteQuery = buildDeleteQueryBySeasonId(tableName, seasonId, batchSize);

        int rowsAffected;
        long totalDeleted = 0;

        do {
            rowsAffected = jdbcTemplate.update(deleteQuery);
            totalDeleted += rowsAffected;
            log.info("Deleted {} rows from {}, total: {}", rowsAffected, tableName, totalDeleted);

            // Configurable pause to reduce DB load
            if (rowsAffected > 0 && waitTimeMs > 0) {
                try {
                    Thread.sleep(waitTimeMs);
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                    log.warn("Deletion interrupted for table {}", tableName);
                    break;
                }
            }
        } while (rowsAffected > 0);

        log.info("Completed deletion from {} for season {}. Total rows deleted: {}", tableName, seasonId, totalDeleted);
        return totalDeleted;
    }

    /**
     * Count records that would be deleted by season ID
     */
    public long countBySeasonId(String tableName, String seasonId) {
        if (StringUtils.isBlank(seasonId)) {
            return 0L;
        }

        String countQuery = buildCountQueryBySeasonId(tableName, seasonId);

        try {
            Long count = jdbcTemplate.queryForObject(countQuery, Long.class);
            return count != null ? count : 0L;
        } catch (Exception e) {
            log.error("Error counting records for table {} with season {}: {}", tableName, seasonId, e.getMessage());
            return 0L;
        }
    }

    /**
     * Build appropriate DELETE query based on table structure using season ID
     */
    private String buildDeleteQueryBySeasonId(String tableName, String seasonId, int batchSize) {
        switch (tableName) {
            case "transfer_market_bid":
                return String.format(
                    "DELETE FROM hm.transfer_market_bid WHERE id IN (" +
                    "SELECT tmb.id FROM hm.transfer_market_bid tmb " +
                    "JOIN hm.transfer_market tm ON tmb.transfer_market_id = tm.id " +
                    "JOIN hm.league l ON tm.league_id = l.id " +
                    "WHERE l.season_id = '%s' AND tmb.deleted = false LIMIT %d)",
                    seasonId, batchSize);

            case "transfer_market":
                return String.format(
                    "DELETE FROM hm.transfer_market WHERE id IN (" +
                    "SELECT tm.id FROM hm.transfer_market tm " +
                    "JOIN hm.league l ON tm.league_id = l.id " +
                    "WHERE l.season_id = '%s' AND tm.deleted = false LIMIT %d)",
                    seasonId, batchSize);

            case "lineup":
                return String.format(
                    "DELETE FROM hm.lineup WHERE id IN (" +
                    "SELECT l.id FROM hm.lineup l " +
                    "JOIN hm.round r ON l.round_id = r.id " +
                    "WHERE r.season_id = '%s' AND l.deleted = false LIMIT %d)",
                    seasonId, batchSize);

            case "user_round_score":
                return String.format(
                    "DELETE FROM hm.user_round_score WHERE id IN (" +
                    "SELECT urs.id FROM hm.user_round_score urs " +
                    "JOIN hm.league l ON urs.league_id = l.id " +
                    "WHERE l.season_id = '%s' AND urs.deleted = false LIMIT %d)",
                    seasonId, batchSize);

            case "team":
                return String.format(
                    "DELETE FROM hm.team WHERE id IN (" +
                    "SELECT t.id FROM hm.team t " +
                    "JOIN hm.league l ON t.league_id = l.id " +
                    "WHERE l.season_id = '%s' AND t.deleted = false LIMIT %d)",
                    seasonId, batchSize);

            case "league_invitation":
                return String.format(
                    "DELETE FROM hm.league_invitation WHERE id IN (" +
                    "SELECT li.id FROM hm.league_invitation li " +
                    "JOIN hm.league l ON li.league_id = l.id " +
                    "WHERE l.season_id = '%s' AND li.deleted = false LIMIT %d)",
                    seasonId, batchSize);

            case "user_notification":
                return String.format(
                    "DELETE FROM hm.user_notification WHERE id IN (" +
                    "SELECT un.id FROM hm.user_notification un " +
                    "WHERE un.created_at < CAST((SELECT s.end_date FROM hm.season s WHERE s.id = '%s') AS TIMESTAMP) " +
                    "AND un.deleted = false LIMIT %d)",
                    seasonId, batchSize);

            case "scheduler_job":
                return String.format(
                    "DELETE FROM hm.scheduler_job WHERE id IN (" +
                    "SELECT sj.id FROM hm.scheduler_job sj " +
                    "WHERE sj.created_at < CAST((SELECT s.end_date FROM hm.season s WHERE s.id = '%s') AS TIMESTAMP) " +
                    "AND sj.deleted = false LIMIT %d)",
                    seasonId, batchSize);

            default:
                throw new IllegalArgumentException("Unsupported table for deletion: " + tableName);
        }
    }

    /**
     * Build appropriate COUNT query based on table structure using season ID
     */
    private String buildCountQueryBySeasonId(String tableName, String seasonId) {
        switch (tableName) {
            case "transfer_market_bid":
                return String.format(
                    "SELECT COUNT(*) FROM hm.transfer_market_bid tmb " +
                    "JOIN hm.transfer_market tm ON tmb.transfer_market_id = tm.id " +
                    "JOIN hm.league l ON tm.league_id = l.id " +
                    "WHERE l.season_id = '%s' AND tmb.deleted = false",
                    seasonId);

            case "transfer_market":
                return String.format(
                    "SELECT COUNT(*) FROM hm.transfer_market tm " +
                    "JOIN hm.league l ON tm.league_id = l.id " +
                    "WHERE l.season_id = '%s' AND tm.deleted = false",
                    seasonId);

            case "lineup":
                return String.format(
                    "SELECT COUNT(*) FROM hm.lineup l " +
                    "JOIN hm.round r ON l.round_id = r.id " +
                    "WHERE r.season_id = '%s' AND l.deleted = false",
                    seasonId);

            case "user_round_score":
                return String.format(
                    "SELECT COUNT(*) FROM hm.user_round_score urs " +
                    "JOIN hm.league l ON urs.league_id = l.id " +
                    "WHERE l.season_id = '%s' AND urs.deleted = false",
                    seasonId);

            case "team":
                return String.format(
                    "SELECT COUNT(*) FROM hm.team t " +
                    "JOIN hm.league l ON t.league_id = l.id " +
                    "WHERE l.season_id = '%s' AND t.deleted = false",
                    seasonId);

            case "league_invitation":
                return String.format(
                    "SELECT COUNT(*) FROM hm.league_invitation li " +
                    "JOIN hm.league l ON li.league_id = l.id " +
                    "WHERE l.season_id = '%s' AND li.deleted = false",
                    seasonId);

            case "user_notification":
                return String.format(
                    "SELECT COUNT(*) FROM hm.user_notification un " +
                    "WHERE un.created_at < CAST((SELECT s.end_date FROM hm.season s WHERE s.id = '%s') AS TIMESTAMP) " +
                    "AND un.deleted = false",
                    seasonId);

            case "scheduler_job":
                return String.format(
                    "SELECT COUNT(*) FROM hm.scheduler_job sj " +
                    "WHERE sj.created_at < (SELECT s.end_date FROM hm.season s WHERE s.id = '%s') " +
                    "AND sj.deleted = false",
                    seasonId);

            default:
                throw new IllegalArgumentException("Unsupported table for counting: " + tableName);
        }
    }
}
